<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsCourses.php');
include('../class/clsHospitalSite.php');
include('../class/clsRotation.php');
include('../class/clsLocations.php');
include('../class/clsStudent.php');


$loggedUserId = $_SESSION["loggedUserId"];
$TimeZone = $_SESSION["loggedUserSchoolTimeZone"];
$loggedUserLocationId = '';
$loggedUserLocationId = $_SESSION["loggedUserLocation"];
$page_title = "Add Rotation";
$rotationId = 0;
$hospitalSiteId = 0;
$parentRotationId = 0;
$parentlocationId = 0;
$sellocationId = 0;
$title = '';
$startDate = date("m/d/Y");
$totalStartTimePeriod = date("Y-m-d h:i:s");
$totalEndTimePeriod = date("Y-m-d h:i:s");
$totalNewStartDateTime = date("Y-m-d h:i:s");
$totalNewEndDateTime = date("Y-m-d 12:00:00");
$endDate = date("m/d/Y", strtotime("+7 days"));
$subRotationStartTime = date('h:i A');
$subRotationEndTime =  date('h:i A', strtotime("+1 hours"));
$courseId = 0;
$duration  = '00:00';
$parentDuration  = '00:00';
$id = '';
$startTime = date('h:i A');
$endTime = date('h:i A');
$RotationRepeatDays = '';
$parentEndDateTime  = date('h:i A');
$bedCrumTitle = 'Add';
$default_courseId = 0;
$parentStartDate = date("m/d/Y");
$parentEndDate = date("m/d/Y");
$parentEndTime = date('h:i A');
$parentStartTime = date('h:i A');
$requiredRotationClass = 'required';
$schoolId = $currentSchoolId;
$locationId = 0;
$default_locationId = 0;

$currentDate = date("m/d/Y");
$nextDate = date("Y/d/m", strtotime("+1 days"));

$objRotation = new clsRotation();

$AllRepeatDays = $objRotation->GetAllRepeatDays();
$listId = 0;
$isSchedule = 0;
$rsStudents = '';
$datesInRange = array();
$dateRangeValues = '';

// Check Schedule is active for school or not
$objDB = new clsDB();
$scheduleActive = $objDB->GetSingleColumnValueFromTable('schools', 'scheduleActive', 'schoolId', $currentSchoolId);
unset($objDB);

// echo '<pre>';
if (isset($_GET['id'])) //Edit Mode
{
	$rotationId = DecodeQueryData($_GET['id']);
	$page_title = "Edit Rotation";
	$bedCrumTitle = 'Edit';

	$row = $objRotation->GetrotationDetailsToAdmin($rotationId, $currentSchoolId);
	$rowrepeat = $objRotation->GetRotationRepeatDaysDetails($rotationId, $currentSchoolId);
	// print_r($rowrepeat);

	$objDB = new clsDB();
	$isSchedule = $objDB->GetSingleColumnValueFromTable('rotation', 'isSchedule', 'rotationId', $rotationId);
	unset($objDB);

	$RotationRepeatDays = array();
	while ($rows = mysqli_fetch_array($rowrepeat)) {
		$RotationRepeatDays[] = $rows['dayOfWeek'];
	}
	if ($row == '') {
		header('location:addrotations.html');
		exit;
	}
	$title  = stripslashes($row['title']);
	$courseId  = ($row['courseId']);
	$rotationId = stripslashes($row['rotationId']);
	$parentRotationId = $row['parentRotationId'];
	$hospitalSiteId = $row['hospitalSiteId'];
	$scheduleHospitalSiteId = $hospitalSiteId;
	$rotationLocationId = $row['locationId'];

	$locationId = 0;
	if ($rotationLocationId  && $parentRotationId > 0) {
		if ($parentRotationId > 0) {
			if (!$rotationLocationId)
				$locationId = $objRotation->GetLocationByRotation($rotationId);
			else
				$locationId  = $rotationLocationId;
		}
	}
	$startDateTime = converFromServerTimeZone($row['startDate'], $TimeZone);
	$startDate = date('m/d/Y', strtotime($startDateTime));
	$startTime = date('h:i A', strtotime($startDateTime));

	$NewStartDate = date('Y-m-d', strtotime($startDateTime));
	$NewStartDateTime = date('h:i:s', strtotime($startDateTime));
	$totalNewStartDateTime = $NewStartDate . ' ' . $NewStartDateTime;

	$endDateTime = converFromServerTimeZone($row['endDate'], $TimeZone);
	$endDate = date('m/d/Y', strtotime($endDateTime));
	$endTime = date('h:i A', strtotime($endDateTime));

	$NewEndDate = date('Y-m-d', strtotime($endDateTime));
	$NewEndDateTime = date('12:00:00', strtotime($endDateTime));
	$totalNewEndDateTime = $NewEndDate . ' ' . $NewEndDateTime;


	if ($isSchedule) {
		//For Clinician Reports
		$rsStudents = $objRotation->GetAssignedStudentsIdForRotation($rotationId);
		$ScheduleDates = $objRotation->GetRotationScheduleDates($rotationId);
		if ($ScheduleDates != '') {
			$repeatDays = explode(',', $ScheduleDates);

			// 	// $currentDate = date('Y-m-d', strtotime($startDate));
			// 	// $currentEndDate = date('Y-m-d', strtotime($endDate));

			// 	// $datesInRange = array(); // Initialize the array to hold the dates

			// 	// while ($currentDate <= $currentEndDate) {
			// 	// 	$dayOfWeek = date("w", strtotime($currentDate));

			// 	// 	if (in_array($dayOfWeek, $repeatDays)) {
			// 	// 		$datesInRange[] = $currentDate; // Store the date
			// 	// 	}

			// 	// 	$currentDate = date('Y-m-d', strtotime($currentDate . ' +1 day'));
			// 	// }

			$dateRangeValues = implode(', ', $repeatDays);
			// 	print_r($repeatDays);
		}
	}

	$duration = ($row['duration'] == null || $row['duration'] == '' || $row['duration'] == '0') ? '00:00' : $row['duration'];
	//-------------------------------------------------------------------
	//This is sub rotation
	//-------------------------------------------------------------------

	if ($parentRotationId > 0) {
		$requiredRotationClass = '';
		$page_title = "Edit Hospital Site";
		$bedCrumTitle = 'Edit';
		$parentStartDateTime = converFromServerTimeZone($row['parentStartDate'], $TimeZone);
		$parentStartDate = date('m/d/Y', strtotime($parentStartDateTime));
		$parentStartTime = date('h:i A', strtotime($parentStartDateTime));
		//$parentStartDate = date('m/d/Y h:i A', strtotime($parentStartDate));
		$parentEndDateTime = converFromServerTimeZone($row['parentEndDate'], $TimeZone);

		$parentEndDate = date('m/d/Y', strtotime($parentEndDateTime));
		$parentEndTime = date('h:i A', strtotime($parentEndDateTime));
		$parentDuration =  stripslashes($row['duration']);
	}
} else if (isset($_GET['perrotationId'])) {
	$page_title = "Add Hospital Site";
	$bedCrumTitle = 'Add';
	$parentRotationId = $_GET['perrotationId'];
	$parentRotationId = DecodeQueryData($parentRotationId);
	$row = $objRotation->GetrotationDetails($parentRotationId, $currentSchoolId);

	if ($row == '') {
		header('location:addrotations.html');
		exit;
	}

	$parentStartDateTime = converFromServerTimeZone($row['startDate'], $TimeZone);

	$parentStartDate = date('m/d/Y', strtotime($parentStartDateTime));
	$StartPeriod = date('Y-m-d', strtotime($parentStartDateTime));

	$parentStartTime = date('h:i A', strtotime($parentStartDateTime));
	$StartTimePeriodStartTime = date('h:i:s', strtotime($parentStartDateTime));
	$totalStartTimePeriod = $StartPeriod . ' ' . $StartTimePeriodStartTime;


	$parentEndDateTime = converFromServerTimeZone($row['endDate'], $TimeZone);
	$parentEndDate = date('m/d/Y', strtotime($parentEndDateTime));
	$StartPeriodEnd = date('Y-m-d', strtotime($parentEndDateTime));

	$parentEndTime = date('h:i A', strtotime($parentEndDateTime));
	$StartTimePeriodEndTime = date('h:i:s', strtotime($parentEndDateTime));
	$totalEndTimePeriod = $StartPeriodEnd . ' ' . $StartTimePeriodEndTime;

	$parentDuration =  stripslashes($row['duration']);
	$parentlocationId =  stripslashes($row['locationId']);
}
//----------------------------------------------------------------------------------------
//Get parent rotations
//----------------------------------------------------------------------------------------
if ($isSchedule)
	$parentRotations = $objRotation->GetParentRotations($currentSchoolId, $loggedUserLocationId);
else
	$parentRotations = $objRotation->GetParentRotations($currentSchoolId, $loggedUserLocationId, $parentRotationId);
//----------------------------------------------------------------------------------------
//Get Course Details
//----------------------------------------------------------------------------------------
if (isset($_GET['courseId']))
	$default_courseId = DecodeQueryData($_GET['courseId']);

$objCourses = new clsCourses();
$courses = $objCourses->GetAllCoursesByLocation($currentSchoolId, $default_courseId);
unset($objCourses);
//----------------------------------------------------------------------------------------
//Get Hospitals
//----------------------------------------------------------------------------------------
$objHospitalSite = new clsHospitalSite();
$hospitalSite = $objHospitalSite->GetAllHospitalSite($currentSchoolId);
$hospitalsiteList = $objHospitalSite->GetAllHospitalSite($currentSchoolId);

//Locations

$objLocations = new clsLocations();
$locations = $objLocations->GetAlllocation($schoolId);
unset($objLocations);

unset($objRotation);

//CREATE OBJECT
$objStudents = new clsStudent();
$totalSchoolStudents = 0;
$rowsSchoolStudents = $objStudents->GetAllSchoolStudentsForAssignToROtation($currentSchoolId, $rankId = 0, 0);
unset($objStudents);

if ($rowsSchoolStudents != '') {
	$totalSchoolStudents = mysqli_num_rows($rowsSchoolStudents);
}
$studentListArray = array();

if ($totalSchoolStudents > 0) {
	while ($row = mysqli_fetch_array($rowsSchoolStudents)) {
		$studentId = $row['studentId'];
		$firstName = stripslashes($row['firstName']);
		$lastName = stripslashes($row['lastName']);
		//$location = stripslashes($row['location']);
		$rank = stripslashes($row['rank']);

		$fullName = $firstName . ' ' . $lastName . ' - ' . $rank;
		$studentListArray[] = array(
			"id" => $studentId,
			"name" => $fullName
		);
	}
}



$hospitalSiteArray = array();
if ($hospitalsiteList != "") {
	while ($rowHospital = mysqli_fetch_assoc($hospitalsiteList)) {
		$hositalId  = $rowHospital['hospitalSiteId'];
		$hospitalName  = stripslashes($rowHospital['title']);
		$clockIn  = $rowHospital['clockIn'];
		$clockOut  = $rowHospital['clockOut'];

		if ($scheduleActive) {
			$hospitalName = ($clockIn != '' && $clockOut != '') ? $hospitalName . ' (' . $clockIn . '-' . $clockOut . ')' : $hospitalName;
		}

		// add Attribute to select for clockIn & clockOut
		$hospitalSiteArray[] = array(
			"id" => $hositalId,
			"name" => $hospitalName,
			"clockIn" => $clockIn,
			"clockOut" => $clockOut
		);
	}
}
$hospitalSiteJson = json_encode($hospitalSiteArray);
$studentListJson = json_encode($studentListArray);
unset($objHospitalSite);


?>
<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
	<!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
	<title><?php echo ($page_title); ?></title>
	<?php include('includes/headercss.php'); ?>
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />


	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker3.min.css">
	<script type="module" src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.esm.js"></script>
	<script nomodule src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.js"></script>
	<style>
		/* .select2-container .select2-selection--multiple {
			min-height: 35px !important;
		} */
		.select2 .select2-container .select2-container--default {
			width: 100% !important;
		}

		.trash-icon {
			display: flex;
			align-items: end;
			margin-bottom: 5px;
			/* margin-top: -20px; */
		}

		ion-icon {
			pointer-events: none;
		}

		.scheduleSection {
			border: 1px solid #ccc;
			padding-top: 15px;
			border-radius: 14px;
			margin-bottom: 10px;
			padding-right: 24px;
		}

		.disabled-link {
			pointer-events: none;
			cursor: not-allowed;
			text-decoration: none;
			background-color: gray;
			border-color: gray;
		}

		.preceptors-input {
			width: 100% !important;
			max-width: 250px !important;
		}

		.trash-icon {
			display: flex;
			align-items: end;
			margin-bottom: 5px;
		}

		.trash-hide {
			visibility: hidden;
		}

		.select-width {
			width: 250px !important;
		}

		ion-icon {
			pointer-events: none;
		}


		.points_table thead {
			width: 100%;
		}

		.points_table tbody {
			height: 300px;
			overflow-y: auto;
			width: 100%;
		}

		.points_table thead tr {
			width: 99%;
		}

		.points_table tr {
			width: 100%;
		}

		.points_table thead,
		.points_table tbody,
		.points_table tr,
		.points_table td,
		.points_table th {
			display: inline-block;
		}

		.points_table thead {
			background: rgb(45, 105, 182);
			color: #fff;
		}

		.points_table tbody td,
		.points_table thead>tr>th {
			float: left;
			/* //border-bottom-width: 0;
         //border: 1px solid;
         //padding: 8px; */

		}

		/*table, th, td {
         border: 1px solid black;
      }*/
		.points_table>tbody>tr>td,
		.points_table>tbody>tr>th,
		.points_table>tfoot>tr>td,
		.points_table>tfoot>tr>th,
		.points_table>thead>tr>td,
		.points_table>thead>tr>th {
			padding: 8px;
			/*height: 50px;
         line-height: 32px;
         */
			text-align: left;

		}

		.odd {
			background: #ffffff;
			color: #000;
		}


		.points_table_scrollbar {
			height: 612px;
			overflow-y: scroll;
		}

		.points_table_scrollbar::-webkit-scrollbar-track {
			-webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.9);
			border-radius: 10px;
			background-color: #444444;
		}

		.points_table_scrollbar::-webkit-scrollbar {
			width: 1%;
			min-width: 5px;
			background-color: #F5F5F5;
		}

		.points_table_scrollbar::-webkit-scrollbar-thumb {
			border-radius: 10px;
			background-color: gray;
			background-image: -webkit-linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.4) 50%, transparent, transparent)
		}

		.form-group {
			margin-bottom: 10px !important;
		}

		.form-control {
			height: 45px;
		}

		.table-border {
			border-radius: 20px;
			overflow: hidden;
			box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
			padding: 15px;
		}

		.points_table thead {
			background: none;
			color: #353535;
			border-bottom: 1px solid #80808059;
		}

		.points_table_scrollbar::-webkit-scrollbar {
			width: 0;
			min-width: 0;
			background-color: #F5F5F5;
		}

		.table-border:hover .points_table_scrollbar::-webkit-scrollbar {
			width: 1%;
			min-width: 3px;
			background-color: #F5F5F5;
		}

		.select2-container--default .select2-selection--single .select2-selection__rendered {
			line-height: 45px !important;
		}

		.required-select2 {
			border-left: solid 3px red !important;
			border-radius: 12px !important;
		}

		.select2-container--default .select2-selection--single {
			background-color: #f6f9f9 !important;
			cursor: default !important;
			height: 45px !important;
			border-radius: 10px !important;
		}

		.select2-container--default .select2-selection--single {
			border: none !important;
		}

		.panel,
		.form-group {
			margin-bottom: 10px;
		}

		.bootstrap-datetimepicker-widget {
			border-radius: 12px !important;
		}

		.form-control {
			height: 45px;
		}

		textarea.form-control {
			height: auto;
		}

		.points_table tbody {
			height: fit-content;
			overflow-y: auto;
			width: 100%;
		}

		input[type="radio"],
		input[type="checkbox"] {
			margin-right: 5px;
		}

		.select2-container--default .select2-selection--multiple {

			margin: auto;
			padding: 10px -2px;
			height: 45px;
			/* background: #01A750; */
			/* color: #fff; */
			color: #555;
			background: #f6f9f9;
			border: none;
			display: flex;
			justify-content: center;
			align-items: center;
		}

		.select2-container--default .select2-selection--multiple .select2-selection__rendered {
			min-height: 45px;
			max-height: 75px;
			height: auto;
			overflow-y: auto;
		}

		.select2-container--default.select2-container--focus .select2-selection--multiple,
		.select2-container--default .select2-selection--multiple,
		.select2-container {
			height: auto !important;
		}

		.datepicker-dropdown {
			z-index: 999 !important;
		}
	</style>
</head>

<body>
	<?php include('includes/header.php'); ?>

	<div class="row margin_zero breadcrumb-bg">
		<div class="container">
			<div class="pull-left">
				<ol class="breadcrumb">
					<li><a href="dashboard.html">Home</a></li>
					<?php
					$queryparams = "";
					if (isset($_GET['courseId'])) { ?>
						<li><a href="courses.html">Courses</a></li>
						<li><a href="rotations.html?courseId=<?php echo (EncodeQueryData($default_courseId)); ?>">Rotation</a></li>
					<?php
						$queryparams = "?courseId=" . $_GET['courseId'];
					} else if (isset($_GET['perrotationId']) || isset($_GET['perrotationId'])) { ?>
						<li><a href="rotations.html">Rotation</a></li>
						<li><a href="addsubrotation.html?rotationId=<?php echo (EncodeQueryData($parentRotationId)); ?>">Hospital Site</a></li>


					<?php } else { ?>
						<li><a href="rotations.html<?php echo ($queryparams); ?>">Rotations</a></li>
					<?php  } ?>
					<li class="active"><?php echo ($bedCrumTitle); ?></li>
				</ol>
			</div>
			<div class="pull-right">
				<a class="btn btn-link" href="scheduleBuilder.html">Schedule Builder</a>
			</div>
		</div>
	</div>

	<div class="container">

		<?php
		if (isset($_GET["status"])) {
			if ($_GET["status"] == "error") {
		?>
				<div class="alert alert-danger alert-dismissible fade in" role="alert">
					<button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
					</button> Error occurred.
				</div>
		<?php   }
		} ?>

		<form id="frmRotations" data-parsley-validate class="form-horizontal" method="POST" action="addrotationsubmit.html?id=<?php echo ($rotationId); ?>">

			<div class="row">
				<div class="col-md-12">
					<!-- Text input-->

					<div class="form-group">
						<!-- <label class="col-md-4 control-label" for="txtTitle"></label> -->
						<div class="col-md-12">
							<?php if (isset($_GET['id'])) //Edit Mode
							{
								if ($parentRotationId > 0 && $isSchedule == 0) { ?>
									<label class="radio-inline">
										<input id="subrotation" class="type checkrotation" value="subrotation" name="type" type="radio">
										Hospital Site
									</label>
								<?php } else if ($isSchedule > 0) { ?>

									<label class="radio-inline">
										<input id="schedule" class="type checkrotation" value="schedule" name="type" type="radio" checked>
										Schedule
									</label>
								<?php } else { ?>
									<label class="radio-inline">
										<input id="rotation" class="type checkrotation" value="rotation" name="type" type="radio" checked>
										Rotation
									</label>
									<!-- <label class="radio-inline col-md-4 control-label">
										<input id="schedule" class="type checkrotation" value="schedule" name="type" type="radio" checked>
										Schedule
									</label> -->
								<?php }
							} else {
								if (isset($_GET['perrotationId']) || isset($_GET['perrotationId'])) { ?>

									<label class="radio-inline">
										<input id="subrotation" class="type checkrotation" value="subrotation" name="type" type="radio">
										Hospital Site
									</label>

								<?php } else { ?>
									<label class="radio-inline">
										<input id="rotation" class="type checkrotation" value="rotation" name="type" type="radio" checked>
										Rotation
									</label>
									<label class="radio-inline">
										<input id="subrotation" class="type checkrotation" value="subrotation" name="type" type="radio">
										Hospital Site
									</label>
									<?php //if ($currentSchoolId == 75 || $currentSchoolId == 129 || $currentSchoolId == 55 || $currentSchoolId == 136) { 
									?>
									<?php if ($scheduleActive) { ?>
										<label class="radio-inline">
											<input id="schedule" class="type checkrotation" value="schedule" name="type" type="radio">
											Schedule By Student
										</label>
									<?php } ?>
									<?php if ($scheduleActive) { ?>
										<!-- <label class="radio-inline">
											<input id="scheduleHospital" class="type checkrotation" value="scheduleHospital" name="type" type="radio">
											Schedule By Hospital Site
										</label> -->
									<?php } ?>
							<?php }
							} ?>
							<br><br>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-12 control-label" for="txtTitle">Title</label>
						<div class="col-md-12">
							<input id="txtTitle" name="txtTitle" value="<?php echo ($title); ?>" required type="text" placeholder="" class="form-control input-md required-input " required="">

						</div>

					</div>
				</div>
				<div class="col-md-6">
					<span class="row margin_zero" id="SubRotationcourse" <?php
																			if (isset($_GET['perrotationId']) || isset($_GET['perrotationId'])) { ?>style="display:none" <?php } ?>>
						<div class="form-group">
							<input type="hidden" name="default_courseId" id="default_courseId" value="<?php echo $default_courseId; ?>">
							<label class="col-md-12 control-label" for="cbocourses">Courses</label>
							<div class="col-md-12 flex-col-reverse">
								<select id="cbocourses" name="cbocourses" class="form-control input-md required-input select2_single" required>
									<option value="" selected>Select</option>
									<?php
									if ($courses != "") {
										while ($row = mysqli_fetch_assoc($courses)) {
											$selcourseId  = $row['courseId'];
											$name  = stripslashes($row['title']);
											$courseEndDate  = stripslashes($row['courseEndDate']);

									?>
											<option value="<?php echo ($selcourseId);  ?>" <?php if ($courseId == $selcourseId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>



										<?php } ?>

									<?php }
									?>
								</select>
							</div>
						</div>
					</span>
				</div>
			</div>
			<div class="row">
				<div class="col-md-6">
					<div class="form-group hospitalSiteClass">
						<input type="hidden" name="default_courseId" id="default_courseId" value="<?php echo $default_courseId; ?>">
						<label class="col-md-12 control-label" for="cbohospitalsites">Hospital Site</label>
						<div class="col-md-12 flex-col-reverse">
							<select id="cbohospitalsites" name="cbohospitalsites" class="form-control input-md required-input select2_single" required>
								<option value="" selected>Select</option>
								<?php
								if ($hospitalSite != "") {
									while ($row = mysqli_fetch_assoc($hospitalSite)) {
										$selhospitalSiteId  = $row['hospitalSiteId'];
										$name  = stripslashes($row['title']);

								?>
										<option value="<?php echo ($selhospitalSiteId); ?>" <?php if ($hospitalSiteId == $selhospitalSiteId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>

								<?php

									}
								}
								?>
							</select>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-md-6">
					<span class="row margin_zero" id="SubRotationSection">
						<div class="form-group">
							<input type="hidden" name="perrotationId" id="perrotationId" value="<?php echo ($parentRotationId); ?>">

							<label class="col-md-12 control-label" for="mainrotation">Rotations</label>
							<div class="col-md-12">
								<select id="mainrotation" name="mainrotation" currentSchoolId="<?php echo ($currentSchoolId); ?>" required class="form-control input-md  select2_single disabledClass" onchange="rotationDateAndTime(this);" data-parsley-errors-container="#error-rotation">
									<option value="" selected>Select Rotation</option>
									<?php
									while ($row = mysqli_fetch_assoc($parentRotations)) {
										$selrotationId  = $row['rotationId'];
										$name  = stripslashes($row['title']);

									?>
										<option value="<?php echo ($selrotationId); ?>" <?php if ($selrotationId == $parentRotationId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>

									<?php

									}
									?>
								</select>
								<?php if (isset($_GET['perrotationId'])) { ?>
									<input type="hidden" name="mainrotation" id="mainrotation" value="<?php echo $parentRotationId; ?>">
								<?php } ?>
								<div id="error-rotation"></div>
							</div>

						</div>
					</span>
				</div>
				<div class="col-md-6">
					<span class="row margin_zero" id="SubRotationLocation">
						<div class="form-group">

							<label class="col-md-12 control-label" for="cbolocation">Location</label>
							<div class="col-md-12">
								<select id="cbolocation" name="cbolocation" class="form-control input-md  select2_single parentlocationId ">
									<!-- <option value="" >Select</option -->
									<?php
									if ($locations != "") {
										while ($row = mysqli_fetch_assoc($locations)) {
											$sellocationId  = $row['locationId'];
											$name  = stripslashes($row['title']);
											// echo $locationId;
									?>
											<?php if (isset($_GET['perrotationId'])) { ?>
												<option value="<?php echo ($sellocationId); ?>" <?php if ($sellocationId == $parentlocationId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
											<?php } else {
											?>

												<option value="<?php echo ($sellocationId); ?>" <?php if ($locationId == $sellocationId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
											<?php } ?>
									<?php

										}
									}
									?>
								</select>
							</div>
						</div>
					</span>
				</div>
			</div>

			<div class="row">
				<div class="col-md-6">
					<div class="form-group sectionRotation">
						<label class="col-md-12 control-label" for="startDate">Start Date</label>

						<div class="col-md-12 col-sm-12 col-xs-12">
							<div class='input-group date w-full relative' id='startDate'>

								<input type='text' name="startDate" id="startDate" class="form-control input-md required-input rotation_date dateInputFormat startDateLatest startDate disabledClass" value="<?php if ($rotationId != '') {
																																																					echo $startDate;
																																																				} else {
																																																					echo $parentStartDate;
																																																				}  ?>" <?php echo $requiredRotationClass; ?> data-parsley-errors-container="#error-txtDate" placeholder="MM-DD-YYYY" />
								<span class="input-group-addon calender-icon">
									<span class="glyphicon glyphicon-calendar"></span>
								</span>
							</div>
							<div id="error-txtDate"></div>
						</div>
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group sectionRotation">
						<label class="col-md-12 control-label" for="startTime">Start Time</label>
						<div class="col-md-12 col-sm-12 col-xs-12">
							<div class='input-group date w-full relative'>

								<input type='text' name="startTime" id="startTime" class="form-control input-md required-input rotation_date startTimeLatest startTime disabledClass" value="<?php if ($rotationId != '') {
																																																	echo date('h:i A', strtotime($startTime));
																																																} else {
																																																	echo date('h:i A', strtotime($parentStartTime));
																																																} ?>" <?php echo $requiredRotationClass; ?> required data-parsley-errors-container="#error-txtStartTime" placeholder="HH-MM" />
								<span class="input-group-addon calender-icon">
									<span class="glyphicon glyphicon-calendar"></span>
								</span>
							</div>
							<div id="error-txtStartTime"></div>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-md-6">
					<div class="form-group sectionRotation">

						<label class="col-md-12 control-label" for="endDate">End Date </label>
						<div class="col-md-12 col-sm-12 col-xs-12">
							<div class='input-group date w-full relative' id='endDate'>

								<input type='text' name="endDate" id="endDate" class="form-control input-md required-input rotation_date dateInputFormat startEndLatest endDate disabledClass" value="<?php if ($rotationId != '') {
																																																			echo $endDate;
																																																		} else {
																																																			echo $parentEndDate;
																																																		} ?>" <?php echo $requiredRotationClass; ?> data-parsley-errors-container="#error-txtendDate" placeholder="MM-DD-YYYY" />
								<span class="input-group-addon calender-icon">
									<span class="glyphicon glyphicon-calendar"></span>
								</span>
							</div>

							<div id="error-txtendDate"></div>
						</div>
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group sectionRotation">

						<label class="col-md-12 control-label" for="endTime">End Time</label>
						<div class="col-md-12 col-sm-12 col-xs-12">
							<div class='input-group date w-full relative'>

								<input type='text' name="endTime" id="endTime" class="form-control input-md required-input rotation_date endTimeLatest endTime disabledClass" value="<?php if ($rotationId != '') {
																																															echo date('h:i A', strtotime($endTime));
																																														} else {
																																															echo date('h:i A', strtotime($parentEndDateTime));
																																														} ?>" <?php echo $requiredRotationClass; ?> data-parsley-errors-container="#error-txtendTime" placeholder="HH-MM" />


								<span class="input-group-addon calender-icon">
									<span class="glyphicon glyphicon-calendar"></span>
								</span>
							</div>

							<div id="error-txtendTime"></div>
						</div>
					</div>
				</div>

			</div>
			<div class="row">
				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-12 control-label" for="duration">Duration In Hours.</label>
						<div class="col-md-12 col-sm-12 col-xs-12">
							<div class='input-group Duration w-full' id='Duration'>
								<input style="display:none;" type="range" value="<?php if ($rotationId != '') {
																						echo $duration;
																					} else {
																						echo ($parentDuration);
																					} ?>" name="duration" id="duration" min="0" max="100" onchange="updateTextInput(this.value);">
								<input readonly class="form-control input-md parentDurationLatest" type="text" id="Inputrange" name="Inputrange" value="<?php if ($rotationId != '') {
																																							echo $duration;
																																						} else {
																																							echo ($parentDuration);
																																						} ?>">
							</div>
						</div>
					</div>



					<div class="form-group RepeatDays">
						<label class="col-md-12 control-label" for="RepeatDays">Select Repeat Days</label>
						<div class="col-md-12 col-sm-12 col-xs-12">
							<div class='input-group table-responsive table-border'>

								<table class="points_table" style="height:100px;width: 100%;" id="datatable-responsive">
									<thead>
										<tr>
											<th style="min-width: 60px;width: 20%;text-align: center;""><input class=" selectall" type="checkbox" id="selectall" name="selectall[0]" checked>All</th>
											<th style="min-width: 60px;width: 80%;">Repeat Days</th>

										</tr>
									</thead>
									<tbody class="points_table_scrollbar">
										<?php
										if ($AllRepeatDays) {

											while ($row = mysqli_fetch_array($AllRepeatDays)) {
												$title = $row['title'];
												$value = $row['value'];
												$checked = "";
												if (is_array($RotationRepeatDays))
													if (in_array($value, $RotationRepeatDays)) {
														$checked = 'checked="true"';
													}

										?>
												<tr>
													<td style="min-width: 60px;width: 20%;text-align: center;">
														<input class="uncheck checkedcount" type="checkbox" value="<?php echo ($value); ?>" name="repeatdays[]" <?php if (isset($_GET['id'])) {
																																									echo $checked;
																																								} else { ?> checked <?php } ?>>
													</td>
													<td style="min-width: 60px; width: 80%;"><?php echo ($title); ?></td>
												</tr>
										<?php
											}
										}
										?>
									</tbody>
								</table>



							</div>
						</div>
					</div>




				</div>
				<br>
				<input type="hidden" id="ishiddenSchedule" class="ishiddenSchedule" name="ishiddenSchedule" value="0">
				<input type="hidden" id="datesRange" name="datesRange" value="<?php echo $dateRangeValues ?>">
				<div class="col-md-12 scheduleSection mt-15" id="scheduleSection">
					<div id="scheduleDiv">
						<div class="row" style="display: flex; margin-bottom: 10px; margin-left: 10px">

							<div class=" trash-hide" style="visibility: hidden;">1</div>
							<div style="padding:0 !important" class="col-md-3">
								<label style="text-align: start; padding: 0; margin-left: 5px; width:100%;" class="col-md-12 control-label" for="cbosemester">Student</label>

							</div>
							<div style="padding:0 !important;margin-left: 5px;" class="col-md-2">
								<label style="text-align: start; padding: 0; margin-left: 15px; width:100%;" class="col-md-12  control-label" for="cbocourses">Date</label>
							</div>
							<div style="padding:0 !important;margin-left: 5px;" class="col-md-2">
								<label style="text-align: start; padding: 0; margin-left: 25px;" class="col-md-12  control-label" for="cbocourses">Hospital Site</label>
							</div>
							<div style="padding:0 !important;margin-left: 5px;" class="col-md-2">
								<label style="text-align: start; padding: 0; margin-left: 35px;" class="col-md-12  control-label" for="startTime">Start Time</label>
							</div>
							<div style="padding:0 !important" class="col-md-1">
								<label style="text-align: start; padding: 0; margin-left: 45px;" class="col-md-12  control-label" for="startTime">End Time</label>
							</div>
							<div style="padding:0 !important" class="col-md-1">
								<div class=" trash-hide">
									<a href="javascript:void(0);" title="Delete"></a>
									<!-- <i class="fa fa-trash-o" aria-hidden="true" name="trash" title="Delete" style="font-size: 18px;margin-top: 6px;"></i></a> -->
								</div>
							</div>

						</div>
						<!-- <hr> -->

						<!-- <div class="row" id="row_1" style="display: flex; margin-bottom: 10px">
							<div class="trash-icon" style="margin-right: 4px;">1</div>

							<div style="padding:0 !important; margin-right: 10px" class="col-md-3">
								<select style="width: 100%;" id="scheduleStudents_1" multiple="multiple" name="scheduleStudents[1][]" class="form-control input-md   parentlocationId isMultiple  " >

								</select>
								<div id="error-scheduleStudents_1"></div>

							</div>

							<div style="padding:0 !important; margin-right: 10px" class="col-md-3">

								
								<input type="text" class="form-control " name="scheduleDate_1" id="scheduleDate_1" />

							</div>

							<div style="padding:0 !important; margin-right: 10px" class="col-md-2">
								<select id="schedulehospitalsites_1" name="schedulehospitalsites[1][]" class="form-control input-md required-input select2_single schedulehospitalsites" required data-parsley-errors-container="#error-schedulehospitalsites_1">
									<option value="" selected>Select</option>

								</select>

								<div id="error-schedulehospitalsites_1"></div>
							</div>

							<div style="padding:0 !important; margin-right: 10px" class="col-md-2">
								<div class='input-group date' id='scheduleStartTime_1'>

									<input type='text' name="scheduleStartTime[1][]" id="scheduleStartTime_1" class="form-control input-md required-input rotation_date scheduleStartTime startTimeLatest" <?php echo $requiredRotationClass; ?> data-parsley-errors-container="#error-scheduleStartTime_1" />
									<span class="input-group-addon">
										<span class="glyphicon glyphicon-calendar"></span>
									</span>
								</div>
								<div id="error-scheduleStartTime_1"></div>


							</div>
							<div style="padding:0 !important; margin-right: 10px" class="col-md-1">
								<div style="width: 180px !important " class='input-group date' id='scheduleEndTime_1'>

									<div class='input-group date' id='scheduleEndTime_1'>

										<input type='text' name="scheduleEndTime[1][]" id="scheduleEndTime_1" class="form-control input-md required-input rotation_date scheduleEndTime endTimeLatest" <?php echo $requiredRotationClass; ?> data-parsley-errors-container="#error-scheduleEndTime_1" />


										<span class="input-group-addon">
											<span class="glyphicon glyphicon-calendar"></span>
										</span>
									</div>

									<div id="error-scheduleEndTime_1"></div>


								</div>
							</div>
						</div> -->
					</div>
					<input style="max-width: 40%; margin: 20px auto;position: relative;left: 15px;" type="button" class="form-control input-md addMoreSchedules" value="Add More Schedules" islast='<?php echo $listId; ?>'>

				</div>

				<div class="form-group m-0">
					<!-- <label class="col-md-2 control-label"></label> -->
					<div class="col-md-12" style="display: flex; justify-content: center; margin: 20px 0;gap: 15px;">
						<button id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button>
						<button id="btnFinish" name="btnFinish" class="btn btn-success" type="button" style="display:none;margin-right: 5px;">Complete</button>

						<?php if (isset($_GET['rotationId'])) { ?>
							<a type="button" href="addsubrotation.html?rotationId=<?php echo EncodeQueryData($subrotationId); ?>" class="btn btn-default">Cancel</a>
						<?php } elseif (isset($_GET['courseId'])) { ?>
							<a type="button" href="rotations.html?courseId=<?php echo EncodeQueryData($default_courseId); ?>" class="btn btn-default">Cancel</a>
						<?php } elseif ($isSchedule) { ?>
							<a type="button" href="addsubrotation.html?rotationId=<?php echo EncodeQueryData($parentRotationId); ?>" class="btn btn-default">Cancel</a>

						<?php } else { ?>
							<a type="button" href="rotations.html" class="btn btn-default">Cancel</a>
						<?php  } ?>
					</div>

				</div>

		</form>
	</div>

	<?php include('includes/footer.php'); ?>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>


	<script type="text/javascript">
		var min_Date = '08/20/2018 08:00 AM';
		var max_Date = '08/25/2018 08:00 AM';

		$(window).load(function() {
			<?php if (isset($_GET['perrotationId'])) { ?>
				calculateDuration();
			<?php } elseif (isset($_GET['id'])) { ?>
				calculateDuration();
			<?php } else { ?>
				calculateDuration2();
			<?php } ?>

			function calculateDuration2() {
				$('#Inputrange').val('00:00');
			}

			var TotalCheckboxCount = $('input[name="repeatdays[]"]').length;
			var CheckedCheckboxCount = $('input[name="repeatdays[]"]:checked').length;
			if (TotalCheckboxCount == CheckedCheckboxCount) {
				$('.selectall').prop('checked', true);
			} else {
				$('.selectall').prop('checked', false);
			}

			// for course drop down disabled 
			<?php if (isset($_GET['courseId'])) { ?>
				$('#cbocourses').val('<?php echo (DecodeQueryData($_GET['courseId'])); ?>').trigger('change');
				$('#cbocourses').prop('disabled', true);
			<?php } ?>

			<?php if (isset($_GET['perrotationId'])) { ?>
				$('#mainrotation').prop('disabled', true);
			<?php  } ?>


			<?php
			if ($parentRotationId > 0) {
			?>
				$('#subrotation').attr('checked', true);
				$('#subrotation').trigger('click');

			<?php } ?>


			$('#startDate').datetimepicker({
				useCurrent: false,
				inline: true,
				sideBySide: true,
				format: 'MM/DD/YYYY'

			});

			$('#endDate').datetimepicker({
				useCurrent: false,
				inline: true,
				sideBySide: true,
				format: 'MM/DD/YYYY'
			});
			$('#startTime, .scheduleStartTime').datetimepicker({
				format: 'hh:mm A',

			});

			$('#endTime, .scheduleEndTime').datetimepicker({
				format: 'hh:mm A',

			});

			<?php if ($rotationId > 0) { ?>
				var yearDate = new Date();
				yearDate.setFullYear(yearDate.getFullYear() + 1);
				$('#endDate').val(yearDate);
			<?php }
			if ($parentRotationId  > 0) { ?>

				$('#startDate').data('DateTimePicker').minDate('<?php echo $parentStartDate; ?>');
				$('#startDate').data('DateTimePicker').maxDate('<?php echo $parentEndDate; ?>');
				$('#endDate').data('DateTimePicker').minDate('<?php echo $parentStartDate ?>');
				$('#endDate').data('DateTimePicker').maxDate('<?php echo $parentEndDate ?>');

			<?php
			}
			?>

			//Set default date
			$(".select2_single").select2();
			$('#select2-cbocourses-container').addClass('required-select2');
			$('#select2-mainrotation-container').addClass('required-select2');
			$('#select2-cbohospitalsites-container').addClass('required-select2');
			// $('#select2-schedulehospitalsites_1-container').addClass('required-select2');
			// $(".isMultiple").select2({
			// 	placeholder: "Select",
			// 	allowClear: true,

			// });
			// $('#scheduleStudents_1').removeAttr('required');
			$('#frmRotations').parsley().on('field:validated', function() {
					// alert('hi')
					var ishiddenSchedule = $('#ishiddenSchedule').val();

					// console.log('hi'+ishiddenSchedule);
					if (ishiddenSchedule != 1)
						$('#scheduleDiv').find('input, select').removeAttr('required');

					var ok = $('.parsley-error').length === 0;
					// console.log('ok' + ok);

				})
				.on('form:submit', function() {

					// $("#mainrotation").prop('disabled', false);
					$(".disabledClass").prop('disabled', false);

					ShowProgressAnimation();
					return true; // Don't submit form for this demo
				});



			//Sub Rotation
			$('.type').trigger("change");
			$('.addMoreSchedules').trigger('click');
			// var newMaxDate = new Date();
			// newMaxDate.setDate(newMaxDate.getDate() + 30); // Example: New max date is 30 days from today

			// $('#scheduleDate_1').datepicker('option', 'maxDate', newMaxDate);
			// // $('.addMoreSchedules').trigger('click');
			// $('#row_2').remove();

			<?php if ($isSchedule) { ?>
				// alert('hello');
				$('#mainrotation').val('<?php echo ($parentRotationId); ?>').trigger('change');
				$('#cbolocation').val('<?php echo ($locationId); ?>').trigger('change');
				// $('#mainrotation').attr(disabled,true);$locationId
				// $("#mainrotation").prop('disabled', true);
				$(".disabledClass").prop('disabled', true);
				var select2Control = $('#scheduleStudents_1');
				select2Control.select2(); // Initialize the Select2 control
				var scheduleHospitalSiteId = '<?php echo ($scheduleHospitalSiteId); ?>';
				var selectedValues = '<?php echo $rsStudents; ?>';
				const scheduleStartTime_1 = '<?php echo date('h:i A', strtotime($startTime)); ?>';
				const scheduleEndTime_1 = '<?php echo date('h:i A', strtotime($endTime)); ?>';
				$('.select2-selection--multiple').addClass('required-select2');
				$('#schedulehospitalsites_1').val(scheduleHospitalSiteId).trigger('change');
				// alert(scheduleHospitalSiteId);


				if (selectedValues !== '') {
					selectedValues = selectedValues.split(",");
					select2Control.val(selectedValues).trigger('change');
				}

				var dateStrings = $('#datesRange').val();
				if (dateStrings != '') {
					var datesArray = dateStrings.split(', ');

					// Convert the dates in the array to Date objects
					var dateObjectsArray = datesArray.map(function(date) {
						var parts = date.split("-");
						return new Date(parts[0], parts[1] - 1, parts[2]);
					});
				}


				// Initialize the datepicker
				// $('#datePicker').datepicker();

				// Set the dates in the datepicker
				$('#scheduleDate_1').datepicker('setDates', dateObjectsArray);
				datesDisabled();
				// $('.addMoreSchedules').hide();
				// $('.update').show();
				$("#row_" + 1 + " input, #row_" + 1 + " select").each(function() {
					$(this).prop("disabled", !$(this).prop("disabled"));
				});
				var rotationId = '<?php echo $rotationId; ?>';
				$('#editDivClass_1').show();
				$('#update_1').hide();
				$('#edit_1').show();
				$('#update_1').attr('rotationId', rotationId);
				$('#btnFinish').show();
				$('#btnSubmit').hide();
				$('.scheduleStartTime_1').val(scheduleStartTime_1);
				$('.scheduleEndTime_1').val(scheduleEndTime_1);
				$('.addMoreSchedules').val('Add More Schedules');

				btnFinish();

			<?php } ?>
		});
		// $('#scheduleDate_1').datepicker({
		// 	multidate: true
		// });

		function updateTextInput(val) {
			document.getElementById('Inputrange').value = val;
		}

		$("#cbohospitalsites").on('change', function() {

			var val = $("#cbohospitalsites option:selected").text();
			var selVal = $("#cbohospitalsites option:selected").val();

			var options = $('#cbolocation option');
			$('#cbolocation option[value]').removeAttr('selected', true).trigger('change');

			if (selVal == '395') {
				$('#cbolocation option[value="109"]').attr('selected', true).trigger('change');
			}

			var values = $.map(options, function(option) {

				return option.text;
			});
			var conData = $.map(options, function(option) {

				var valdata = option.value;
				return valdata;
			});



			if (jQuery.inArray(val, values) != -1) {
				var indexText = values.indexOf(val);

				var indexVal = conData[indexText];

				$('#cbolocation option[value="' + indexVal + '"]').attr('selected', true).trigger('change');

			}
		});
		$(".type").change(function() {

			if ($("#subrotation").prop("checked")) {

				document.getElementById("cbocourses").required = false;
				$('#SubRotationSection').removeClass('hide');
				$('#mainrotation').prop('required', true);
				// FOR HIDE COURSE DD AT SUBROTATION TIME
				$('#SubRotationcourse').addClass('hide');
				$('#SubRotationLocation').removeClass('hide');
				$('#mainrotation').removeAttr('required', true);
				$('#scheduleSection').addClass('hide');
				$('.RepeatDays').removeClass('hide');
				$('#cbocourses').removeAttr('required');
				$('#ishiddenSchedule').val(0);
				$('#scheduleDiv').find('input').removeAttr('required');
				$('#scheduleDiv').find('input, select').removeAttr('required');
				$('.hospitalSiteClass').removeClass('hide');
				$('#cbohospitalsites').attr('required', true);

				$('#scheduleDiv').find('input, select').removeAttr('required');
				$('#btnFinish').hide();
				$('#btnSubmit').show();
				$(".disabledClass").prop('disabled', false);

			} else if ($("#rotation").is(":checked")) {

				$('#mainrotation').removeAttr('required');
				$('#SubRotationSection').addClass('hide');
				// FOR SHOW COURSE DD AT ROTATION TIME
				$('#SubRotationcourse').removeClass('hide');
				$('#SubRotationLocation').addClass('hide');
				$('#mainrotation').removeAttr('required', true);
				$('#scheduleSection').addClass('hide');
				$('.RepeatDays').removeClass('hide');
				$('#cbocourses').attr('required', true);
				$('#cbohospitalsites').attr('required', true);
				$('#ishiddenSchedule').val(0);
				$('#scheduleDiv').find('input').removeAttr('required');
				$('#scheduleDiv').find('input, select').removeAttr('required');
				$('.hospitalSiteClass').removeClass('hide');
				$('#scheduleSection').find('input, select').removeAttr('required');
				$('#btnFinish').hide();
				$('#btnSubmit').show();
				$(".disabledClass").prop('disabled', false);


				// } else if ($("#schedule").is(":checked") || $("#scheduleHospital").is(":checked")) {
			} else if ($("#schedule").is(":checked")) {
				$('#SubRotationSection').removeClass('hide');
				$('#mainrotation').prop('required', true);
				// FOR HIDE COURSE DD AT SUBROTATION TIME
				$('#SubRotationcourse').addClass('hide');
				$('.hospitalSiteClass').addClass('hide');
				$('#cbohospitalsites').removeAttr('required', true);
				$('#cbocourses').removeAttr('required');
				$('#SubRotationLocation').removeClass('hide');
				// $('#mainrotation').removeAttr('required', true);
				$('.RepeatDays').addClass('hide');
				$('#scheduleSection').removeClass('hide');
				$('#ishiddenSchedule').val(1);
				$("#mainrotation, #cbohospitalsites").select2().val('').trigger("change");
				$('#select2-mainrotation-container').addClass('required-select2');
				// $("#mainrotation, #cbohospitalsites").select2().val(0).trigger("change");
				// $('#scheduleDiv').find('input').attr('required', true);
				// $('#scheduleDiv').find('input, select').attr('required', true);
				// $('#scheduleDiv').find('input, select').attr('required', true);
				$(".disabledClass").prop('disabled', true);
				$("#mainrotation,#cbolocation").prop('disabled', false);
				$('.select2-container--default').css('width', '100%');
				var isschedule = '<?php echo $isSchedule; ?>';
				// console.log('isschedule' + isschedule);
				if (isschedule == 0) {
					$('#btnFinish').show();
					$('#btnSubmit').hide();
				}
			}
			// for schedule by hospital sites

			// if ($("#scheduleHospital").is(":checked")) {
			// 	$('#txtTitle').prop('readonly', true);
			// } else {
			// 	$('#txtTitle').prop('readonly', false);
			// }
		});


		<?php
		if (isset($_GET['perrotationId'])) {	?>
			document.getElementById("cbocourses").required = false;
			$('#mainrotation').prop('disabled', true);
		<?php }
		?>

		$(function() {

			$('#startDate').datetimepicker({
				format: 'MM/DD/YYYY'
			}).on('dp.change', function(e) {

				var incrementDay = moment(new Date(e.date));
				$('#endDate').data('DateTimePicker').minDate(incrementDay);
				$(this).data("DateTimePicker");

			});

			$('#endDate').datetimepicker({
				format: 'MM/DD/YYYY'
			}).on('dp.change', function(e) {
				var decrementDay = moment(new Date(e.date));
				decrementDay.subtract(1, 'days');
				$('#startDate').data('DateTimePicker').maxDate(decrementDay);
				$(this).data("DateTimePicker");

			});

			$('#startTime').datetimepicker({
				format: 'hh:mm A'
			}).on('dp.change', function(e) {
				calculateDuration();
			});

			$('#endTime').datetimepicker({
				format: 'hh:mm A'
			}).on('dp.change', function(e) {
				calculateDuration();
			});



		});

		function calculateTimeDifference(startTime, endTime) {
			var startMoment = moment(startTime, "hh:mm A");
			var endMoment = moment(endTime, "hh:mm A");

			if (endMoment.isBefore(startMoment)) {
				endMoment.add(1, 'day'); // Handle time periods crossing midnight
			}

			var duration = moment.duration(endMoment.diff(startMoment));
			var hours = duration.hours();
			var minutes = duration.minutes();

			return padZero(hours) + ":" + padZero(minutes);
		}

		function padZero(num) {
			return (num < 10 ? '0' : '') + num;
		}

		// 		/* 11052021 */
		function calculateDuration() {
			var startTimeForDiff = $('.startTime').val();
			var endTimeForDiff = $('.endTime').val();
			var timeDifference = calculateTimeDifference(startTimeForDiff, endTimeForDiff);
			$('#Inputrange').val(timeDifference);
			// return false;

			// start_date = new Date($('#startTime').data("DateTimePicker").date());
			// date_ob = new Date(start_date);

			// end_date = new Date($('#endTime').data("DateTimePicker").date());
			// date_ob_end = new Date(end_date);

			// // year as 4 digits (YYYY)
			// var year = date_ob.getFullYear();

			// // month as 2 digits (MM)
			// var month = ("0" + (date_ob.getMonth() + 1)).slice(-2);

			// // date as 2 digits (DD)
			// var date = ("0" + date_ob.getDate()).slice(-2);

			// // hours as 2 digits (hh)
			// var hours = ("0" + date_ob.getHours()).slice(-2);

			// // minutes as 2 digits (mm)
			// var minutes = ("0" + date_ob.getMinutes()).slice(-2);

			// // seconds as 2 digits (ss)
			// var seconds = ("0" + date_ob.getSeconds()).slice(-2);

			// var dateFuture = year + "/" + month + "/" + date + " " + hours + ":" + minutes + ":" + seconds;
			// // console.log(dateFuture);
			// // End date

			// // year as 4 digits (YYYY)
			// var year = date_ob_end.getFullYear();

			// // month as 2 digits (MM)
			// var month = ("0" + (date_ob_end.getMonth() + 1)).slice(-2);

			// // date as 2 digits (DD)
			// var date1 = ("0" + date_ob_end.getDate()).slice(-2);
			// var date = +date + +1;

			// // hours as 2 digits (hh)
			// var hours = ("0" + date_ob_end.getHours()).slice(-2);

			// // minutes as 2 digits (mm)
			// var minutes = ("0" + date_ob_end.getMinutes()).slice(-2);

			// // seconds as 2 digits (ss)
			// var seconds = ("0" + date_ob_end.getSeconds()).slice(-2);

			// // date & time as YYYY-MM-DD hh:mm:ss format: 
			// var dateNow = year + "/" + month + "/" + date + " " + hours + ":" + minutes + ":" + seconds;

			// // console.log(dateNow);

			// function timeDiffCalc(dateNow, dateFuture) {
			// 	let diffInMilliSeconds = Math.abs(dateFuture - dateNow) / 1000;

			// 	// calculate days
			// 	const days = Math.floor(diffInMilliSeconds / 86400);
			// 	diffInMilliSeconds -= days * 86400;
			// 	// console.log('calculated days', days);

			// 	// calculate hours
			// 	const hours = Math.floor(diffInMilliSeconds / 3600) % 24;
			// 	diffInMilliSeconds -= hours * 3600;
			// 	// console.log('calculated hours', hours);

			// 	// calculate minutes
			// 	const minutes = Math.floor(diffInMilliSeconds / 60) % 60;
			// 	diffInMilliSeconds -= minutes * 60;
			// 	// console.log('minutes', minutes);

			// 	let difference = '';
			// 	if (days > 0) {
			// 		difference += (days === 1) ? `${days} day, ` : `${days} days, `;
			// 	}

			// 	$('#Inputrange').val(hours + ":" + minutes);

			// 	difference += (hours === 0 || hours === 1) ? `${hours} hour, ` : `${hours} hours, `;

			// 	difference += (minutes === 0 || hours === 1) ? `${minutes} minutes` : `${minutes} minutes`;

			// 	return difference;
			// }

			// console.log(timeDiffCalc(new Date(dateNow), new Date(dateFuture)));



		}

		//created by tejas	

		$('#selectall').click(function() {
			//alert('');
			if ($(this).is(':checked')) {
				$('.uncheck').prop('checked', true);
			} else {
				$('.uncheck').removeAttr('checked');

			}
		});

		$('.checkedcount').click(function() {

			var TotalCheckboxCount = $('input[name="repeatdays[]"]').length;

			var CheckedCheckboxCount = $('input[name="repeatdays[]"]:checked').length;

			if (TotalCheckboxCount == CheckedCheckboxCount) {
				$('.selectall').prop('checked', true);
			} else {
				$('.selectall').prop('checked', false);
			}
		});

		//DONE BY Asharani 24 March 2021 Wednesday

		function rotationDateAndTime(eleObj) {
			var selrotationId = (eleObj.value);
			var currentSchoolId = $(eleObj).attr('currentSchoolId');
			var isschedule = '<?php echo $isSchedule; ?>';
			// if (isschedule == 0) {
			$.ajax({
				type: "GET",
				url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_get_rotationdateandtime.html",
				data: {
					id: selrotationId,
					currentSchoolId: currentSchoolId,
					type: 'rotation',
					timeZone: '<?php echo $TimeZone; ?>'
				},

				success: function(result) {
					if (result) {
						var rotationDetails = JSON.parse(result);
						var startDateLatest = rotationDetails.startDate;

						var startEndLatest = rotationDetails.endDate;

						var startTimeLatest = rotationDetails.startTime;
						var date = new Date();
						var date = moment(date).format('MM/DD/YYYY');

						var fulldatetimeStart = date + " " + startTimeLatest;

						var endTimeLatest = rotationDetails.endTime;
						var fulldatetimeEnd = date + " " + endTimeLatest;

						var parentDurationLatest = rotationDetails.parentDuration;
						parentDurationLatest = (parentDurationLatest == '') ? '00:00' : parentDurationLatest;

						$('.startDateLatest').val(startDateLatest);
						$('.startEndLatest').val(startEndLatest);

						$('.startTimeLatest').val(startTimeLatest);
						$('.endTimeLatest').val(endTimeLatest);
						$(".parentDurationLatest").val(parentDurationLatest);

						datesDisabled();
						if ($("#schedule").is(":checked")) {
							var startDate = new Date(startDateLatest);
							var endDate = new Date(startEndLatest);
							populateDatesDropdown(startDate, endDate, 1);

						}

					}

				}
			});
			// }
		}
		/* 12052021 */

		// for schedule
		function getDates(startDate, endDate) {
			var dates = [];
			var currentDate = new Date(startDate);

			while (currentDate <= endDate) {
				dates.push(new Date(currentDate));
				currentDate.setDate(currentDate.getDate() + 1);
			}

			return dates;
		}

		function populateDatesDropdown(startDate, endDate, isId = 0) {
			var dates = getDates(startDate, endDate);
			$('#scheduleDate_' + isId).empty();
			// Populate options
			for (var i = 0; i < dates.length; i++) {
				var formatedDate = formatDate(dates[i]);
				var option = $('<option>').val(formatedDate);
				var option = $('<option>').text(formatedDate);

				// Append the dropdown to a container element
				$('#scheduleDate_' + isId).append(option);

			}

		}

		// Helper function to format date as yyyy-mm-dd
		function formatDate(date) {
			var year = date.getFullYear();
			var month = String(date.getMonth() + 1).padStart(2, '0');
			var day = String(date.getDate()).padStart(2, '0');
			return month + '/' + day + '/' + year;
		}

		// Create dropdown Options
		// function createOptions(listArray, selectElementId) {
		// 	var selectElement = $('#' + selectElementId);

		// 	$.each(listArray, function(index, element) {
		// 		var option = $('<option>');
		// 		option.val(element.id);
		// 		option.text(element.name);

		// 		selectElement.append(option);
		// 	});
		// }

		function createOptions(listArray, selectElementId) {
			var selectElement = $('#' + selectElementId);

			$.each(listArray, function(index, element) {
				var option = $('<option>', {
					value: element.id,
					text: element.name,
					'data-clockin': element.clockIn, // Add clockIn as a data attribute
					'data-clockout': element.clockOut // Add clockOut as a data attribute
				});

				selectElement.append(option);
			});
		}
		var hospitalSiteArray = <?php echo $hospitalSiteJson; ?>;
		var studentArray = <?php echo $studentListJson; ?>;

		createOptions(hospitalSiteArray, 'schedulehospitalsites_1');
		createOptions(studentArray, 'scheduleStudents_1');

		// Schedule module
		//Add more Schedules
		$('.addMoreSchedules').click(function() {

			var islast = $(this).attr('islast');
			// console.log('islast' + islast);
			// if (islast > 0) {
			// 	saveSchedule(islast);
			// }
			// Validate the form using Parsley's validate method
			var buttonText = $(this).val();
			// console.log('buttonText' + buttonText);
			if (buttonText == 'Save') {
				$(this).val('loading...');
				$(this).prop('disabled', true);
				if (islast > 0) {
					var isValid = $('#frmRotations').parsley().validate();
					// console.log('isValid' + isValid);
					if (isValid) {
						// Perform additional actions if validation passes
						$("#mainrotation").prop('disabled', false);
						// ShowProgressAnimation();
						saveSchedule(islast, 0, function(respData) {
							// Do something with the respData here
							// console.log(respData);

							if (respData['status'] == 'Success') {
								$('.addMoreSchedules').val('Add More Schedules');
								$('.addMoreSchedules').prop('disabled', false)
								alertify.success('Schedule Saved');
								$('#scheduleDiv').find('input').prop('disabled', true);
								$('#scheduleDiv').find('input, select').prop('disabled', true);
								$(".disabledClass").prop('disabled', true);
								$('.deleteRow').remove();
								$('#editDivClass_' + islast).show();
								$('#update_' + islast).attr('rotationId', respData['rotationId']);
								btnFinish();
								$('.update').hide();
								$('.edit').show();
								return false;
							} else {
								alertify.error('Error Occured');
								return false;
							}

						});
						// resp = saveSchedule(islast);
						// console.log(resp);
						return false;
					} else {
						$(this).val('Save');
						$(this).prop('disabled', false);
						btnFinish();
						return false;
					}

				}
			}

			var listId = parseInt(islast) + 1;
			$(this).attr('islast', listId);

			getHospitalsitetimes(listId);


			var divHtml = `
			<div class="row" id="row_${listId}" isrow ="${listId}"  style="display: flex; margin-bottom: 10px">
				<div class="" style="margin-right: 4px;min-width: 30px;float: right;text-align: end;display: flex; justify-content: center; align-items: center;">${listId}</div>

				<div style="padding:0 !important; margin-right: 10px" class="col-md-3">
					<select style="width: 100%;" id="scheduleStudents_${listId}" multiple name="scheduleStudents[${listId}][]"
						class="form-control input-md  isMultiple required-input "
						required data-parsley-errors-container="#error-scheduleStudents_${listId}">

					</select>
					<div id="error-scheduleStudents_${listId}"></div>

				</div>

				<div style="padding:0 !important; margin-right: 10px" class="col-md-2">

				

					<input type="text" class="form-control required-input scheduleDateClass" name="scheduleDate[${listId}][]" id="scheduleDate_${listId}" required
						data-parsley-errors-container="#error-scheduleDate_${listId}"/>
					<div id="error-scheduleDate_${listId}"></div>
				</div>

				<div style="padding:0 !important; margin-right: 10px" class="col-md-2">
					<select id="schedulehospitalsites_${listId}" name="schedulehospitalsites[${listId}][]"
						class="form-control input-md required-input select2_single schedulehospitalsites" required
						data-parsley-errors-container="#error-schedulehospitalsites_${listId}">
						<option value="" selected>Select</option>

					</select>

					<div id="error-schedulehospitalsites_${listId}"></div>
				</div>

				<div style="padding:0 !important; margin-right: 10px" class="col-md-2">
					<div class="input-group date w-full relative" id="scheduleStartTimes_${listId}">

						<input type="text" name="scheduleStartTime[${listId}][]" id="scheduleStartTime_${listId}"
							class="form-control input-md required-input rotation_date scheduleStartTime scheduleStartTime_${listId} " <?php echo
																																		$requiredRotationClass; ?> data-parsley-errors-container="#error-scheduleStartTime_${listId}" />
						<span class="input-group-addon calender-icon">
							<span class="glyphicon glyphicon-calendar"></span>
						</span>
					</div>
					<div id="error-scheduleStartTime_${listId}"></div>


				</div>
				<div style="padding:0 !important; margin-right: 10px" class="col-md-1">
					<div style="width: 180px !important " class="input-group date" id="scheduleEndTimes_${listId}">

						<div class="input-group date w-full relative" id="scheduleEndTime_${listId}">

							<input type="text" name="scheduleEndTime[${listId}][]" id="scheduleEndTime_${listId}"
								class="form-control input-md required-input rotation_date scheduleEndTime scheduleEndTime_${listId} " <?php echo
																																		$requiredRotationClass; ?> data-parsley-errors-container="#error-scheduleEndTime_${listId}" />


							<span class="input-group-addon calender-icon">
								<span class="glyphicon glyphicon-calendar"></span>
							</span>


						</div>

						<div id="error-scheduleEndTime_${listId}"></div>

					</div>
				</div>
				<div style="padding:0 !important; margin-right: 10px" class="col-md-1">
							<div class="trash-icon" style="margin-left:80px !important"> 
								<a  href="javascript:void(0);" id="deleteRow_${listId}" class="deleteRow" deleteRowId="${listId}" title="Delete" >
									<i class="fa fa-trash-o" aria-hidden="true" name="trash" title="Delete" style="font-size: 18px;margin-top: 6px;"></i>
								</a>
								<div class="editDivClass trash-icon" id="editDivClass_${listId}" style="display:none;">
								<a  href="javascript:void(0);" id="edit_${listId}" class="edit" editId="${listId}" title="Edit" >
								
									<i class="fa fa-pencil" aria-hidden="true" style="font-size: 18px;margin-top: 6px;"></i>
								</a>
								<a  href="javascript:void(0);" id="update_${listId}" class="update" updateId="${listId}" title="Save" style="display:none;">
								
									<i class="fa fa-check-square" aria-hidden="true" style="font-size: 18px;margin-top: 6px;"></i>

								</a> 
								</div>

							</div>
				</div>
				
			</div> <hr>`;
			$('#scheduleDiv').append(divHtml);

			$("#scheduleStudents_" + listId).select2({
				placeholder: "Select",
			});

			var startDate = $('.startDate').val();
			var endDate = $('.endDate').val();
			// $('#scheduleDate_' + listId).datepicker({
			// 	multidate: true,
			// 	// startDate: startDate, // Disable dates before the start date
			// 	// endDate: endDate // Disable dates after the end date
			// });
			$('.scheduleDateClass').datepicker({
				multidate: true,
				startDate: startDate, // Disable dates before the start date
				endDate: endDate // Disable dates after the end date
			});
			datesDisabled();

			var hospitalSiteArray = <?php echo $hospitalSiteJson; ?>;
			var studentArray = <?php echo $studentListJson; ?>;

			createOptions(hospitalSiteArray, 'schedulehospitalsites_' + listId);
			createOptions(studentArray, 'scheduleStudents_' + listId);

			var startDate = $('.startDate').val();
			var endDate = $('.endDate').val();
			var startTime = $('.startTime').val();
			var endTime = $('.endTime').val();

			var startTime = $('.scheduleStartTime_' + islast).val();
			var endTime = $('.scheduleEndTime_' + islast).val();



			if (islast > 0) {
				selectedStudents = $('#scheduleStudents_' + islast).val();
				// console.log('selectedStudents' + selectedStudents);
				if (selectedStudents !== '') {
					// selectedStudents = selectedStudents.split(",");
					$('#scheduleStudents_' + listId).val(selectedStudents).trigger('change');
				}
			}

			populateDatesDropdown(startDate, endDate, listId);
			$("#schedulehospitalsites_" + listId).select2();
			$('#select2-schedulehospitalsites_' + listId + '-container').addClass('required-select2');
			$('.select2-selection--multiple').addClass('required-select2');
			$('.scheduleEndTime, .scheduleStartTime').datetimepicker({
				format: 'hh:mm A',

			});
			if (islast > 0) {
				selectedStudents = $('#scheduleStudents_' + islast).val();
				$('.scheduleStartTime_' + listId).val(startTime);
				$('.scheduleEndTime_' + listId).val(endTime);
				console.log('selectedStudents' + selectedStudents);
				if (selectedStudents !== '') {
					// selectedStudents = selectedStudents.split(",");
					$('#scheduleStudents_' + listId).val(selectedStudents).trigger('change');
				}

				selectedhospitalsites = $('#schedulehospitalsites_' + islast).val();

				if (selectedhospitalsites !== '') {
					// selectedStudents = selectedStudents.split(",");
					$('#schedulehospitalsites_' + listId).val(selectedhospitalsites).trigger('change');
				}

			} else {
				$('#deleteRow_1').hide();
			}
			getHospitalsitetimes(listId);
			$(this).val('Save');
			$(this).prop('disabled', false)

			btnFinish();
		});
		$('.ishiddenSchedule').change(function() {
			// $(document).on('change', '.ishiddenSchedule', function() {
			console.log('hi');
		});

		function saveSchedule(id, rotationId, callback) {
			var selectedStudentValues = $('#scheduleStudents_' + id).val();
			var selectedDateValues = $('#scheduleDate_' + id).val();
			var selectedhospitalsiteId = $('#schedulehospitalsites_' + id).val();
			var selectedScheduleStartTime = $('.scheduleStartTime_' + id).val();
			var selectedScheduleEndTime = $('.scheduleEndTime_' + id).val();
			var parentRotationId = $('#mainrotation').val();
			var title = $('#txtTitle').val();
			var locationId = $('#cbolocation').val();
			var courseId = $('#cbocourses').val();
			var schoolId = '<?php echo $currentSchoolId; ?>';

			$.ajax({
				type: "POST",
				url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_SaveSchedule.html",
				data: {
					selectedStudentValues: selectedStudentValues,
					selectedDateValues: selectedDateValues,
					selectedhospitalsiteId: selectedhospitalsiteId,
					selectedScheduleStartTime: selectedScheduleStartTime,
					selectedScheduleEndTime: selectedScheduleEndTime,
					parentRotationId: parentRotationId,
					title: title,
					locationId: locationId,
					courseId: courseId,
					schoolId: schoolId,
					rotationId: rotationId
				},

				success: function(data) {
					// console.log(data);
					var respData = JSON.parse(data);
					// Call the callback function with the response data
					callback(respData);
				}
			});
		}

		$(document).on('click', '.deleteRow', function() {
			var lastrowId = $('#scheduleDiv').children().last().attr('isrow');
			console.log('lastrowId' + lastrowId);
			var deleteRowId = $(this).attr('deleteRowId');
			$('#row_' + deleteRowId).remove();
			if (deleteRowId == lastrowId)
				var lastrowId = parseInt(lastrowId) - 1;

			$('.addMoreSchedules').attr('islast', lastrowId);
			var islast = $('.addMoreSchedules').attr('islast');
			$('.addMoreSchedules').val('Add More Schedules');
			btnFinish();
		});

		function btnFinish() {
			var btnText = $('.addMoreSchedules').val();
			if (btnText == 'Save') {
				$("#btnFinish").prop('disabled', true);
			} else {
				$("#btnFinish").prop('disabled', false);
			}
		}

		$('#btnFinish').click(function() {
			// if (islast > 0) {
			var isValid = $('#frmRotations').parsley().validate();
			if (isValid) {
				window.location.href = 'rotations.html';
			}
			// }
		});

		function datesDisabled() {
			var startDate = $('.startDate').val();
			var endDate = $('.endDate').val();
			$('.scheduleDateClass').datepicker({
				multidate: true,
				startDate: startDate, // Disable dates before the start date
				endDate: endDate // Disable dates after the end date
			});
			$('.scheduleDateClass').datepicker('setStartDate', startDate);
			$('.scheduleDateClass').datepicker('setEndDate', endDate);
		}

		$(document).on('click', '.edit', function() {
			editId = $(this).attr('editId');
			$("#row_" + editId + " input, #row_" + editId + " select").each(function() {
				$(this).prop("disabled", !$(this).prop("disabled"));
			});
			$(this).hide();
			$('#update_' + editId).show();
		});

		$(document).on('click', '.update', function() {
			updateId = $(this).attr('updateId');
			rotationId = $(this).attr('rotationId');

			saveSchedule(updateId, rotationId, function(respData) {

				if (respData['status'] == 'Success') {
					alertify.success('Updated');
					$("#row_" + updateId + " input, #row_" + updateId + " select").each(function() {
						$(this).prop("disabled", !$(this).prop("disabled"));
					});
					$(".disabledClass").prop('disabled', true);
					$('.deleteRow').remove();
					$('#editDivClass_' + updateId).show();
					$('#update_' + updateId).attr('rotationId', respData['rotationId']);
					btnFinish();
					$('#update_' + updateId).hide();
					$('#edit_' + updateId).show();
					return false;
				} else {
					alertify.error('Error Occured');
					return false;
				}

			});

		});
		// $("#schedulehospitalsites_1").select2();

		function getHospitalsitetimes(Id) {

			$(document).on('change', '#schedulehospitalsites_' + Id, function() {
				// Get the selected option

				var selectedOption = $(this).find('option:selected');

				// Retrieve the data attributes
				var clockIn = selectedOption.data('clockin');
				var clockOut = selectedOption.data('clockout');

				$('.scheduleStartTime_' + Id).val(clockIn);
				$('.scheduleEndTime_' + Id).val(clockOut);
			});
		}
	</script>
</body>

</html>
